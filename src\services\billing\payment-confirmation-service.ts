/**
 * Serviço para notificar administradores sobre confirmações de pagamento
 * Enviado quando um aluno confirma que realizou um pagamento via PIX
 */

import { createAdminClient } from '@/services/supabase/server';
import { NotificationDispatcher } from '../notifications/channels/notification-dispatcher';

export interface PaymentConfirmationNotificationData {
  paymentId: string;
  tenantId: string;
  studentId: string;
  amount: number;
  currency?: string;
  dueDate: string;
  description?: string;
  planName: string;
  confirmedAt?: string;
}

export interface PaymentConfirmationResult {
  success: boolean;
  error?: string;
  data?: {
    notificationsSent: number;
    adminEmails: string[];
    errors: string[];
  };
}

/**
 * Notifica todos os administradores de um tenant sobre confirmação de pagamento
 */
export async function notifyAdminPaymentConfirmation(
  data: PaymentConfirmationNotificationData
): Promise<PaymentConfirmationResult> {
  try {
    const supabase = await createAdminClient();

    // 1. Buscar dados do aluno
    const { data: studentData, error: studentError } = await supabase
      .from('students')
      .select(`
        id,
        users (
          id,
          email,
          first_name,
          last_name,
          full_name
        )
      `)
      .eq('id', data.studentId)
      .single();

    // Log para depuração - remover após correção
    console.log('Student data structure:', JSON.stringify(studentData, null, 2));

    if (studentError || !studentData) {
      console.error('Erro ao buscar dados do aluno:', studentError);
      return {
        success: false,
        error: 'Dados do aluno não encontrados'
      };
    }

    // Verificar se os dados do usuário existem
    if (!studentData.users) {
      console.error('Dados do usuário do aluno não encontrados');
      return {
        success: false,
        error: 'Dados do usuário do aluno não encontrados'
      };
    }

    // 2. Buscar dados da academia
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('id, name, slug')
      .eq('id', data.tenantId)
      .single();

    if (tenantError || !tenantData) {
      console.error('Erro ao buscar dados da academia:', tenantError);
      return {
        success: false,
        error: 'Dados da academia não encontrados'
      };
    }

    // 3. Buscar todos os administradores do tenant
    const { data: adminUsers, error: adminError } = await supabase
      .from('users')
      .select('id, email, first_name, last_name, full_name')
      .eq('tenant_id', data.tenantId)
      .eq('role', 'admin')
      .is('deleted_at', null);

    if (adminError) {
      console.error('Erro ao buscar administradores:', adminError);
      return {
        success: false,
        error: 'Erro ao buscar administradores'
      };
    }

    if (!adminUsers || adminUsers.length === 0) {
      console.warn(`Nenhum administrador encontrado para o tenant ${data.tenantId}`);
      return {
        success: false,
        error: 'Nenhum administrador encontrado'
      };
    }

    // 4. Preparar dados para notificação
    const student = studentData.users as any;
    const studentName = student.full_name || `${student.first_name} ${student.last_name || ''}`.trim();
    const academyName = tenantData.name;
    const confirmedAt = data.confirmedAt || new Date().toISOString();

    // URL para o perfil do aluno
    const baseUrl = process.env.NEXT_PUBLIC_BASE_DOMAIN;
    const studentProfileUrl = `${tenantData.slug}.${baseUrl}//perfil/${student.id}`;

    // 5. Criar notificações para cada administrador
    const dispatcher = new NotificationDispatcher();
    const notificationsToSend = [];
    const adminEmails: string[] = [];
    const errors: string[] = [];

    for (const admin of adminUsers) {
      const adminName = admin.full_name || `${admin.first_name} ${admin.last_name || ''}`.trim();
      
      notificationsToSend.push({
        tenantId: data.tenantId,
        userId: admin.id,
        type: 'payment' as const,
        category: 'alert' as const,
        priority: 'high' as const,
        title: 'Confirmação de Pagamento Recebida',
        message: `${studentName} confirmou o pagamento de ${new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: data.currency || 'BRL'
        }).format(data.amount)} e aguarda aprovação.`,
        channels: ['email', 'in_app'] as ('email' | 'in_app')[],
        templateId: 'payment_confirmation_admin',
        variables: {
          academyName,
          adminName,
          paymentId: data.paymentId,
          amount: data.amount,
          currency: data.currency || 'BRL',
          dueDate: data.dueDate,
          description: data.description,
          studentName,
          studentEmail: student.email,
          studentUserId: student.id,
          planName: data.planName,
          studentProfileUrl,
          confirmedAt,
          tenantSlug: tenantData.slug
        }
      });

      adminEmails.push(admin.email);
    }

    // 6. Enviar notificações em lote
    console.log(`📤 Enviando ${notificationsToSend.length} notificações de confirmação de pagamento...`);
    
    const batchResults = await dispatcher.dispatchBatch(notificationsToSend);
    let notificationsSent = 0;

    // 7. Processar resultados
    for (let i = 0; i < batchResults.length; i++) {
      const result = batchResults[i];
      const adminEmail = adminEmails[i];

      if (result.success) {
        console.log(`✅ Notificação de confirmação de pagamento enviada para ${adminEmail}`);
        notificationsSent++;
      } else {
        console.error(`❌ Erro ao enviar notificação para ${adminEmail}:`, result.errors);
        errors.push(`Erro ao enviar para ${adminEmail}: ${result.errors?.join(', ') || 'Erro desconhecido'}`);
      }
    }

    // 8. Log da operação
    console.log(`📊 Resumo da notificação de confirmação de pagamento:
      - Pagamento ID: ${data.paymentId}
      - Aluno: ${studentName}
      - Valor: ${new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: data.currency || 'BRL'
      }).format(data.amount)}
      - Administradores notificados: ${notificationsSent}/${adminUsers.length}
      - Erros: ${errors.length}`);

    return {
      success: notificationsSent > 0,
      data: {
        notificationsSent,
        adminEmails,
        errors
      }
    };

  } catch (error) {
    console.error('Erro ao notificar administradores sobre confirmação de pagamento:', error);
    return {
      success: false,
      error: 'Erro interno do servidor'
    };
  }
}

/**
 * Função auxiliar para buscar dados de um pagamento específico
 */
export async function getPaymentDataForNotification(paymentId: string) {
  try {
    const supabase = await createAdminClient();

    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .select(`
        id,
        tenant_id,
        amount,
        currency,
        due_date,
        description,
        student_id,
        updated_at,
        memberships!inner (
          id,
          plan_id,
          plans!inner (
            id,
            title
          )
        )
      `)
      .eq('id', paymentId)
      .single();

    if (paymentError || !paymentData) {
      return {
        success: false,
        error: 'Pagamento não encontrado'
      };
    }

    return {
      success: true,
      data: {
        paymentId: paymentData.id,
        tenantId: paymentData.tenant_id,
        studentId: paymentData.student_id,
        amount: paymentData.amount,
        currency: paymentData.currency,
        dueDate: paymentData.due_date,
        description: paymentData.description,
        planName: (paymentData.memberships as any)?.plans?.title || 'Plano não encontrado',
        confirmedAt: paymentData.updated_at
      } as PaymentConfirmationNotificationData
    };

  } catch (error) {
    console.error('Erro ao buscar dados do pagamento:', error);
    return {
      success: false,
      error: 'Erro interno do servidor'
    };
  }
}
